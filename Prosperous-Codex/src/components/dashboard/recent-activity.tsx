"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useTranslations } from 'next-intl';
import {
  ChevronRight,
  CheckCircle,
  MessageSquare,
  UserPlus,
  Upload,
  Edit,
  User,
  Calendar,
  Plus,
  FileText,
  Settings
} from 'lucide-react';
import { DashboardStats, FormattedActivity, ActivityMapping } from '@/types/dashboard';
import { ActivityLogEntry } from '@/lib/task-master/types';

interface IconWithBackgroundProps {
  variant?: 'success' | 'neutral' | 'brand' | 'warning' | 'error';
  icon?: React.ReactNode;
}

// Activity type to icon and variant mapping
const activityMappings: Record<string, ActivityMapping> = {
  upload: {
    icon: <Upload className="h-4 w-4" />,
    variant: 'neutral',
  },
  comment: {
    icon: <MessageSquare className="h-4 w-4" />,
    variant: 'neutral',
  },
  comment_edit: {
    icon: <Edit className="h-4 w-4" />,
    variant: 'neutral',
  },
  comment_delete: {
    icon: <MessageSquare className="h-4 w-4" />,
    variant: 'warning',
  },
  status_change: {
    icon: <Settings className="h-4 w-4" />,
    variant: 'brand',
  },
  assignment: {
    icon: <User className="h-4 w-4" />,
    variant: 'brand',
  },
  due_date: {
    icon: <Calendar className="h-4 w-4" />,
    variant: 'neutral',
  },
  completion: {
    icon: <CheckCircle className="h-4 w-4" />,
    variant: 'success',
  },
  creation: {
    icon: <Plus className="h-4 w-4" />,
    variant: 'success',
  },
  update: {
    icon: <Edit className="h-4 w-4" />,
    variant: 'neutral',
  },
  task_creation: {
    icon: <Plus className="h-4 w-4" />,
    variant: 'success',
  },
  task_completion: {
    icon: <CheckCircle className="h-4 w-4" />,
    variant: 'success',
  },
  project_details_edit: {
    icon: <Edit className="h-4 w-4" />,
    variant: 'neutral',
  },
  event_log_edit: {
    icon: <FileText className="h-4 w-4" />,
    variant: 'neutral',
  },
};

function IconWithBackground({ variant = 'brand', icon }: IconWithBackgroundProps) {
  const variantClasses = {
    success: 'bg-green-100 text-green-600 dark:bg-emerald-900/20 dark:text-emerald-400',
    neutral: 'bg-neutral-200 text-neutral-600 dark:bg-neutral-800 dark:text-neutral-400',
    brand: 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400',
    warning: 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400',
    error: 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400',
  };

  return (
    <div className={`flex h-8 w-8 items-center justify-center rounded-full ${variantClasses[variant]}`}>
      {icon || <CheckCircle className="h-4 w-4" />}
    </div>
  );
}

// Helper function to format relative time
function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  }
}

// Helper function to format activity data
function formatActivity(activity: ActivityLogEntry): FormattedActivity {
  const mapping = activityMappings[activity.activityType] || {
    icon: <Settings className="h-4 w-4" />,
    variant: 'neutral' as const,
  };

  return {
    id: activity.id,
    icon: mapping.icon,
    variant: mapping.variant,
    title: activity.description,
    description: activity.username ? `by ${activity.username}` : '',
    time: formatRelativeTime(activity.createdAt),
  };
}

export function RecentActivity() {
  const t = useTranslations('dashboard.activity');
  const [activities, setActivities] = useState<FormattedActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard stats for recent activity
  useEffect(() => {
    const fetchRecentActivity = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/dashboard/stats');

        if (!response.ok) {
          throw new Error('Failed to fetch recent activity');
        }

        const data: DashboardStats = await response.json();
        const formattedActivities = data.recentActivity.map(formatActivity);
        setActivities(formattedActivities);
      } catch (err) {
        console.error('Error fetching recent activity:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        // Set empty activities on error
        setActivities([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentActivity();
  }, []);

  return (
    <div className="flex w-full flex-col items-start gap-4">
      <div className="flex w-full items-center justify-between">
        <span className="text-base font-semibold text-foreground">
          {t('title')}
        </span>
        <Button variant="neutral-tertiary" size="sm">
          View all
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      <div className="flex w-full flex-col items-start rounded-md border border-border bg-card/50 backdrop-blur-sm shadow-sm">
        {loading ? (
          <div className="flex w-full items-center justify-center px-4 py-8">
            <span className="text-sm text-muted-foreground">Loading recent activity...</span>
          </div>
        ) : error ? (
          <div className="flex w-full items-center justify-center px-4 py-8">
            <span className="text-sm text-muted-foreground">Unable to load recent activity</span>
          </div>
        ) : activities.length === 0 ? (
          <div className="flex w-full items-center justify-center px-4 py-8">
            <span className="text-sm text-muted-foreground">No recent activity</span>
          </div>
        ) : (
          activities.map((activity, index) => (
            <div key={activity.id} className="w-full">
              <div className="flex w-full items-center gap-4 px-4 py-4">
                <IconWithBackground variant={activity.variant} icon={activity.icon} />
                <div className="flex grow shrink-0 basis-0 flex-col items-start gap-1">
                  <span className="w-full text-sm font-medium text-foreground">
                    {activity.title}
                  </span>
                  {activity.description && (
                    <span className="w-full text-xs text-muted-foreground">
                      {activity.description}
                    </span>
                  )}
                </div>
                <span className="text-sm text-muted-foreground">
                  {activity.time}
                </span>
              </div>
              {index < activities.length - 1 && (
                <div className="h-px w-full bg-border" />
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}
