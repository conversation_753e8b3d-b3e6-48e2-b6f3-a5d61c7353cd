import { getDatabase } from './database';
// User import removed as it's not used directly in this file
import { FieldMapper } from '../task-master/field-mapping';
import {
  ErrorFactory,
  isTaskMasterError
} from '../task-master/errors';
import {
  Project,
  Task,
  ProjectTeamMember,
  ProjectFile,
  ProjectComment,
  ActivityLogEntry,
  ProjectEditHistory,
  CreateProjectInput,
  CreateTaskInput,
  ProjectDbRow,
  TaskDbRow,
  ProjectTeamMemberDbRow,
  ProjectCommentDbRow,
  ActivityLogEntryDbRow,
  ProjectEditHistoryDbRow,
  DataMapper
} from '../task-master/types';
import { AuthorizationService } from '../task-master/authorization';
import { sanitize } from '../task-master/sanitization';

// All interfaces are now imported from ../task-master/types.ts
// This removes duplication and ensures consistency

export class TaskMasterService {
  private db = getDatabase();

  /**
   * Create a new project
   */
  async createProject(userId: number, projectData: CreateProjectInput): Promise<Project> {
    const {
      title,
      description,
      full_description: fullDescription, // Handle snake_case from API middleware
      event_log: eventLog, // Handle snake_case from API middleware
      status = 'todo',
      priority = 'medium',
      progress = 0,
      due_date: dueDate, // Handle snake_case from API middleware
      assigned_to: assignedTo, // Handle snake_case from API middleware
      visibility = 'public',
      tags = []
    } = projectData;

    // Sanitize and validate input
    const sanitizedTitle = sanitize.projectTitle(title);
    if (!sanitizedTitle) {
      throw ErrorFactory.invalidInput('title', title, 'Title is required and cannot be empty');
    }

    const sanitizedDescription = description ? sanitize.projectDescription(description) : null;
    const sanitizedFullDescription = fullDescription ? sanitize.projectDescription(fullDescription) : null;
    const sanitizedEventLog = eventLog ? sanitize.projectEventLog(eventLog) : null;
    const sanitizedTags = tags.map(tag => sanitize.projectTitle(tag)).filter(Boolean);

    if (status && !['todo', 'inProgress', 'completed'].includes(status)) {
      throw ErrorFactory.invalidInput('status', status, 'Status must be todo, inProgress, or completed');
    }

    if (priority && !['low', 'medium', 'high'].includes(priority)) {
      throw ErrorFactory.invalidInput('priority', priority, 'Priority must be low, medium, or high');
    }

    try {
      // Use database transaction for atomic operation
      const transaction = this.db.transaction(() => {
        // Insert project
        const projectStmt = this.db.prepare(`
          INSERT INTO projects (title, description, full_description, event_log, status, priority, progress, due_date, visibility, created_by, assigned_to)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const result = projectStmt.run(
          sanitizedTitle,
          sanitizedDescription,
          sanitizedFullDescription,
          sanitizedEventLog,
          status,
          priority,
          progress,
          dueDate || null,
          visibility,
          userId,
          assignedTo || null
        );

        const projectId = result.lastInsertRowid as number;

        // Add tags if provided
        if (sanitizedTags.length > 0) {
          const tagStmt = this.db.prepare(`
            INSERT INTO project_tags (project_id, tag_name)
            VALUES (?, ?)
          `);

          for (const tag of sanitizedTags) {
            tagStmt.run(projectId, tag);
          }
        }

        // Add project creator as team member with 'owner' role
        const teamMemberStmt = this.db.prepare(`
          INSERT INTO project_team_members (project_id, user_id, role, added_by)
          VALUES (?, ?, ?, ?)
        `);

        teamMemberStmt.run(projectId, userId, 'owner', userId);

        // Log activity within transaction
        const activityStmt = this.db.prepare(`
          INSERT INTO activity_log (project_id, user_id, activity_type, description, created_at)
          VALUES (?, ?, ?, ?, datetime('now'))
        `);

        activityStmt.run(projectId, userId, 'creation', `Created project "${sanitizedTitle}"`);

        // No initial edit history entries during project creation
        // Edit history will only be created when content is actually edited

        return projectId;
      });

      const projectId = transaction();

      // Return the created project
      const project = this.getProjectById(projectId);
      if (!project) {
        throw ErrorFactory.databaseOperation('retrieve', 'Failed to retrieve created project');
      }

      return project;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error creating project:', error);
      throw ErrorFactory.databaseOperation('create', `Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get project by ID with basic details (optimized for performance)
   */
  getProjectById(projectId: number, options?: {
    includeTasks?: boolean;
    includeActivity?: boolean;
    includeFiles?: boolean;
    includeComments?: boolean;
    includeTeamMembers?: boolean;
  }): Project {
    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    const {
      includeTasks = false,
      includeActivity = false,
      includeFiles = false,
      includeComments = false,
      includeTeamMembers = false
    } = options || {};

    try {
      // Get basic project data with edit tracking fields
      const projectStmt = this.db.prepare(`
        SELECT p.*,
               u1.username as created_by_username,
               u2.username as assigned_to_username,
               u3.username as full_description_last_edited_by_username,
               u4.username as event_log_last_edited_by_username
        FROM projects p
        LEFT JOIN users u1 ON p.created_by = u1.id
        LEFT JOIN users u2 ON p.assigned_to = u2.id
        LEFT JOIN users u3 ON p.full_description_last_edited_by = u3.id
        LEFT JOIN users u4 ON p.event_log_last_edited_by = u4.id
        WHERE p.id = ?
      `);

      const projectRow = projectStmt.get(projectId) as ProjectDbRow;
      if (!projectRow) {
        throw ErrorFactory.notFound('Project', projectId);
      }

      // Convert database row to API format
      const project = DataMapper.projectFromDb(projectRow);

      // Always include tags (lightweight)
      const tagsStmt = this.db.prepare(`
        SELECT tag_name FROM project_tags WHERE project_id = ?
      `);
      const tags = tagsStmt.all(projectId) as { tag_name: string }[];

      // Conditionally load heavy data based on options
      const result: Project = {
        ...FieldMapper.dbToApi(project),
        tags: tags.map(t => t.tag_name),
        teamMembers: [],
        files: [],
        comments: [],
        tasks: [],
        activity: []
      };

      if (includeTeamMembers) {
        const teamStmt = this.db.prepare(`
          SELECT ptm.*, u.username, u.email
          FROM project_team_members ptm
          JOIN users u ON ptm.user_id = u.id
          WHERE ptm.project_id = ?
        `);
        const teamMemberRows = teamStmt.all(projectId) as ProjectTeamMemberDbRow[];
        // CRITICAL FIX: Use enhanced safe mapping to prevent member.name undefined issues
        result.teamMembers = DataMapper.teamMembersFromDb(teamMemberRows);
      }

      if (includeFiles) {
        result.files = this.getProjectFiles(projectId);
      }

      if (includeComments) {
        result.comments = this.getProjectComments(projectId);
      }

      if (includeTasks) {
        result.tasks = this.getProjectTasks(projectId);
      }

      if (includeActivity) {
        result.activity = this.getProjectActivity(projectId);
      }

      return result;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error getting project by ID:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get project by ID with full details (backward compatibility)
   */
  getProjectByIdFull(projectId: number): Project {
    return this.getProjectById(projectId, {
      includeTasks: true,
      includeActivity: true,
      includeFiles: true,
      includeComments: true,
      includeTeamMembers: true
    });
  }

  /**
   * Get all projects for a user with optimized loading
   */
  getUserProjects(userId: number, options?: {
    includeDetails?: boolean;
    limit?: number;
    offset?: number;
    status?: string;
  }): Project[] {
    // Validate input
    if (!userId || userId <= 0) {
      throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');
    }

    const {
      includeDetails = false,
      limit = 50,
      offset = 0,
      status
    } = options || {};

    try {
      let query = `
        SELECT p.*,
               u1.username as created_by_username,
               u2.username as assigned_to_username,
               u3.username as full_description_last_edited_by_username,
               u4.username as event_log_last_edited_by_username
        FROM projects p
        LEFT JOIN users u1 ON p.created_by = u1.id
        LEFT JOIN users u2 ON p.assigned_to = u2.id
        LEFT JOIN users u3 ON p.full_description_last_edited_by = u3.id
        LEFT JOIN users u4 ON p.event_log_last_edited_by = u4.id
        WHERE (
          p.visibility = 'public' OR
          (p.visibility = 'private' AND (
            p.created_by = ? OR
            p.assigned_to = ? OR
            p.id IN (SELECT project_id FROM project_team_members WHERE user_id = ?)
          ))
        )
      `;

      const params: (string | number)[] = [userId, userId, userId];

      // Add status filter if provided
      if (status) {
        query += ` AND p.status = ?`;
        params.push(status);
      }

      query += ` ORDER BY p.updated_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const stmt = this.db.prepare(query);
      const projectRows = stmt.all(...params) as ProjectDbRow[];

      // Convert to API format and optionally include details
      return projectRows.map(projectRow => {
        try {
          const project = DataMapper.projectFromDb(projectRow);

          // Always include tags (lightweight)
          const tagsStmt = this.db.prepare(`
            SELECT tag_name FROM project_tags WHERE project_id = ?
          `);
          const tags = tagsStmt.all(project.id) as { tag_name: string }[];

          const result: Project = {
            ...FieldMapper.dbToApi(project),
            tags: tags.map(t => t.tag_name),
            teamMembers: [],
            files: [],
            comments: [],
            tasks: [],
            activity: []
          };

          // Always include the latest comment for hover preview functionality
          try {
            const latestCommentStmt = this.db.prepare(`
              SELECT pc.*, u.username as author_username, u.email as author_email
              FROM project_comments pc
              JOIN users u ON pc.author_id = u.id
              WHERE pc.project_id = ?
              ORDER BY pc.created_at DESC
              LIMIT 1
            `);
            const latestCommentRow = latestCommentStmt.get(project.id) as ProjectCommentDbRow | undefined;
            if (latestCommentRow) {
              result.comments = [DataMapper.commentFromDb(latestCommentRow)];
            }
          } catch (commentError) {
            console.warn(`Error loading latest comment for project ${project.id}:`, commentError);
            // Keep comments as empty array if there's an error
          }

          // Only load heavy data if explicitly requested
          if (includeDetails) {
            // Get team members
            const teamStmt = this.db.prepare(`
              SELECT ptm.*, u.username, u.email
              FROM project_team_members ptm
              JOIN users u ON ptm.user_id = u.id
              WHERE ptm.project_id = ?
            `);
            const teamMemberRows = teamStmt.all(project.id) as ProjectTeamMemberDbRow[];
            result.teamMembers = teamMemberRows.map(DataMapper.teamMemberFromDb);

            // Get recent files (limited)
            result.files = this.getProjectFiles(project.id, { limit: 5 });

            // Get recent comments (limited)
            result.comments = this.getProjectComments(project.id, { limit: 5 });

            // Get task summary (count only for performance)
            const taskCountStmt = this.db.prepare(`
              SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed
              FROM tasks WHERE project_id = ?
            `);
            const taskSummary = taskCountStmt.get(project.id) as { total: number; completed: number };
            result.taskSummary = taskSummary;
          }

          return result;
        } catch (projectError) {
          console.warn(`Error loading details for project ${projectRow.id}:`, projectError);
          // Return project with minimal data if details fail to load
          const project = DataMapper.projectFromDb(projectRow);
          return {
            ...project,
            tags: [],
            teamMembers: [],
            files: [],
            comments: [],
            tasks: [],
            activity: []
          };
        }
      });
    } catch (error) {
      console.error('Error getting user projects:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve projects for user ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get project count for a user (for pagination)
   */
  getUserProjectCount(userId: number, status?: string): number {
    // Validate input
    if (!userId || userId <= 0) {
      throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');
    }

    try {
      let query = `
        SELECT COUNT(*) as count
        FROM projects p
        WHERE (
          p.visibility = 'public' OR
          (p.visibility = 'private' AND (
            p.created_by = ? OR
            p.assigned_to = ? OR
            p.id IN (SELECT project_id FROM project_team_members WHERE user_id = ?)
          ))
        )
      `;

      const params: (string | number)[] = [userId, userId, userId];

      // Add status filter if provided
      if (status) {
        query += ` AND p.status = ?`;
        params.push(status);
      }

      const stmt = this.db.prepare(query);
      const result = stmt.get(...params) as { count: number };
      return result.count;
    } catch (error) {
      console.error('Error getting user project count:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve project count for user ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update project
   */
  async updateProject(projectId: number, user: { id: string; role: string }, updateData: UpdateProjectInput): Promise<void> {
    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    if (!user?.id) {
      throw ErrorFactory.invalidInput('user', user, 'Valid user is required');
    }

    // Validate update data
    if (updateData.status && !['todo', 'inProgress', 'completed'].includes(updateData.status)) {
      throw ErrorFactory.invalidInput('status', updateData.status, 'Status must be todo, inProgress, or completed');
    }

    if (updateData.priority && !['low', 'medium', 'high'].includes(updateData.priority)) {
      throw ErrorFactory.invalidInput('priority', updateData.priority, 'Priority must be low, medium, or high');
    }

    if (updateData.visibility && !['public', 'private'].includes(updateData.visibility)) {
      throw ErrorFactory.invalidInput('visibility', updateData.visibility, 'Visibility must be public or private');
    }

    try {
      // Get full project details for authorization
      const project = this.getProjectById(projectId);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(user as any, {
        project,
        resource: project
      });

      // Check if user can update this project
      AuthorizationService.assertCan(authContext, 'update', 'project');

      const fields: string[] = [];
      const values: (string | number | boolean | null)[] = [];

      // Field mapping: API camelCase -> Database snake_case
      const fieldMapping: Record<string, string> = {
        'fullDescription': 'full_description',
        'eventLog': 'event_log',
        'dueDate': 'due_date',
        'assignedTo': 'assigned_to',
        'completedDate': 'completed_date',
        'createdBy': 'created_by',
        'createdAt': 'created_at',
        'updatedAt': 'updated_at'
      };

      // Build dynamic update query with field mapping
      Object.entries(updateData).forEach(([key, value]) => {
        if (value !== undefined) {
          const dbColumnName = fieldMapping[key] || key; // Use mapping or fallback to original
          fields.push(`${dbColumnName} = ?`);

          // Apply additional sanitization for text fields to ensure line breaks are preserved
          let processedValue = value;
          if (typeof value === 'string') {
            if (key === 'fullDescription' || key === 'description') {
              processedValue = sanitize.projectDescription(value);
            } else if (key === 'eventLog') {
              processedValue = sanitize.projectEventLog(value);
            }
          }

          values.push(processedValue);
        }
      });

      if (fields.length === 0) {
        return; // No updates needed
      }

      fields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(projectId);

      const stmt = this.db.prepare(`
        UPDATE projects
        SET ${fields.join(', ')}
        WHERE id = ?
      `);

      // Get current project data BEFORE the update for edit tracking
      const currentProject = this.getProjectById(projectId);

      const result = stmt.run(...values);

      if (result.changes === 0) {
        throw ErrorFactory.databaseOperation('update', 'No rows were updated');
      }

      // Recalculate project progress if status changed (progress should be based on actual task completion)
      if (updateData.status) {
        this.updateProjectProgressSync(projectId);
      }

      // Log activity for significant changes (outside transaction to avoid blocking)
      try {
        const userId = parseInt(user.id);
        if (updateData.status) {
          // Get current project title for better activity description
          const projectStmt = this.db.prepare('SELECT title FROM projects WHERE id = ?');
          const project = projectStmt.get(projectId) as { title: string } | undefined;
          const projectTitle = project?.title || 'Untitled Project';

          const statusDisplayMap = {
            'todo': 'To Do',
            'inProgress': 'In Progress',
            'completed': 'Completed'
          };

          const statusDisplay = statusDisplayMap[updateData.status as keyof typeof statusDisplayMap] || updateData.status;
          await this.logActivity(userId, 'column_move', `Moved "${projectTitle}" to ${statusDisplay}`, projectId);
        }
        if (updateData.assignedTo !== undefined) {
          await this.logActivity(userId, 'assignment', `Updated assignment`, projectId);
        }
        if (updateData.dueDate) {
          await this.logActivity(userId, 'due_date', `Updated due date`, projectId);
        }
        if (updateData.status === 'completed') {
          await this.logActivity(userId, 'completion', `Completed project`, projectId);
        }

        // Log activity for project field edits with version control
        // Handle both camelCase and snake_case field names

        // Check for full_description (snake_case from frontend)
        if (updateData.full_description !== undefined) {
          await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'full_description', updateData.full_description, currentProject?.fullDescription);
        }
        // Check for fullDescription (camelCase fallback)
        else if (updateData.fullDescription !== undefined) {
          await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'full_description', updateData.fullDescription, currentProject?.fullDescription);
        }

        // Check for event_log (snake_case from frontend)
        if (updateData.event_log !== undefined) {
          await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'event_log', updateData.event_log, currentProject?.eventLog);
        }
        // Check for eventLog (camelCase fallback)
        else if (updateData.eventLog !== undefined) {
          await this.handleProjectFieldEditWithCurrentData(projectId, userId, 'event_log', updateData.eventLog, currentProject?.eventLog);
        }
      } catch (logError) {
        console.warn('Failed to log project update activity:', logError);
      }
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error updating project:', error);
      throw ErrorFactory.databaseOperation('update', `Failed to update project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Toggle project visibility between public and private
   */
  async toggleProjectVisibility(projectId: number, user: { id: string; role: string }): Promise<{ visibility: 'public' | 'private' }> {
    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    if (!user?.id) {
      throw ErrorFactory.invalidInput('user', user, 'Valid user is required');
    }

    try {
      // Get full project details for authorization
      const project = this.getProjectById(projectId);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(user as any, {
        project,
        resource: project
      });

      // Check if user can update this project (must be owner or admin)
      AuthorizationService.assertCan(authContext, 'update', 'project');

      // Toggle visibility
      const newVisibility = project.visibility === 'public' ? 'private' : 'public';

      const stmt = this.db.prepare(`
        UPDATE projects
        SET visibility = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);

      const result = stmt.run(newVisibility, projectId);

      if (result.changes === 0) {
        throw ErrorFactory.databaseOperation('update', 'No rows were updated');
      }

      // Log activity
      try {
        const userId = parseInt(user.id);
        await this.logActivity(userId, 'update', `Changed project visibility to ${newVisibility}`, projectId);
      } catch (logError) {
        console.warn('Failed to log visibility change activity:', logError);
      }

      return { visibility: newVisibility };
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error toggling project visibility:', error);
      throw ErrorFactory.databaseOperation('update', `Failed to toggle project visibility ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete project
   */
  async deleteProject(projectId: number, user: { id: string; role: string }): Promise<void> {
    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    if (!user?.id) {
      throw ErrorFactory.invalidInput('user', user, 'Valid user is required');
    }

    try {
      // Get full project details for authorization
      const project = this.getProjectById(projectId);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(user as any, {
        project,
        resource: project
      });

      // Check if user can delete this project (must be owner)
      AuthorizationService.assertCan(authContext, 'delete', 'project', 'own');

      const stmt = this.db.prepare(`
        DELETE FROM projects WHERE id = ?
      `);

      const result = stmt.run(projectId);

      if (result.changes === 0) {
        throw ErrorFactory.databaseOperation('delete', 'No rows were deleted');
      }

      // Log activity (best effort)
      try {
        const userId = parseInt(user.id);
        await this.logActivity(userId, 'update', `Deleted project "${project.title}"`, projectId);
      } catch (logError) {
        console.warn('Failed to log project deletion activity:', logError);
      }
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error deleting project:', error);
      throw ErrorFactory.databaseOperation('delete', `Failed to delete project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // REMOVED: Duplicate getProjectComments method - using the optimized paginated version below

  /**
   * Add comment to project
   */
  async addComment(projectId: number, user: { id: string; role: string }, content: string, parentCommentId?: number): Promise<ProjectComment> {
    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    if (!user?.id) {
      throw ErrorFactory.invalidInput('user', user, 'Valid user is required');
    }

    const sanitizedContent = sanitize.commentContent(content);
    if (!sanitizedContent) {
      throw ErrorFactory.invalidInput('content', content, 'Comment content is required and cannot be empty');
    }

    try {
      // Universal comment access: Verify project exists but don't restrict to team members
      const project = this.getProjectById(projectId);

      if (!project) {
        throw ErrorFactory.notFound('project', projectId, 'Project not found');
      }

      const userId = parseInt(user.id);

      const stmt = this.db.prepare(`
        INSERT INTO project_comments (project_id, parent_comment_id, author_id, content)
        VALUES (?, ?, ?, ?)
      `);

      const result = stmt.run(projectId, parentCommentId || null, userId, sanitizedContent);
      const commentId = result.lastInsertRowid as number;

      // Log activity
      await this.logActivity(userId, 'comment', `Added comment`, projectId);

      // Get the created comment with user info
      const commentStmt = this.db.prepare(`
        SELECT pc.*, u.username as author_username, u.email as author_email
        FROM project_comments pc
        JOIN users u ON pc.author_id = u.id
        WHERE pc.id = ?
      `);

      const commentRow = commentStmt.get(commentId) as ProjectCommentDbRow;
      if (!commentRow) {
        throw ErrorFactory.databaseOperation('retrieve', 'Failed to retrieve created comment');
      }

      return DataMapper.commentFromDb(commentRow);
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error adding comment:', error);
      throw ErrorFactory.databaseOperation('create', `Failed to add comment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update comment content with edit history tracking
   */
  async updateComment(commentId: number, user: { id: string; role: string }, content: string): Promise<ProjectComment> {
    // Validate input
    if (!commentId || commentId <= 0) {
      throw ErrorFactory.invalidInput('commentId', commentId, 'Comment ID must be a positive number');
    }

    if (!user?.id) {
      throw ErrorFactory.invalidInput('user', user, 'Valid user is required');
    }

    const sanitizedContent = sanitize.commentContent(content);
    if (!sanitizedContent) {
      throw ErrorFactory.invalidInput('content', content, 'Comment content is required and cannot be empty');
    }

    try {
      // Get existing comment for authorization and edit history
      const existingCommentStmt = this.db.prepare(`
        SELECT pc.*, u.username as author_username, u.email as author_email
        FROM project_comments pc
        JOIN users u ON pc.author_id = u.id
        WHERE pc.id = ?
      `);

      const existingCommentRow = existingCommentStmt.get(commentId) as ProjectCommentDbRow;
      if (!existingCommentRow) {
        throw ErrorFactory.notFound('comment', commentId, 'Comment not found');
      }

      const existingComment = DataMapper.commentFromDb(existingCommentRow);

      // Skip update if content hasn't changed
      if (existingComment.content === sanitizedContent) {
        return existingComment;
      }

      // Get project for authorization
      const project = this.getProjectById(existingComment.projectId);

      // Check if user can update this comment (author or moderator+)
      const userId = parseInt(user.id);
      const isAuthor = existingComment.authorId === userId;
      const isModerator = ['moderator', 'admin'].includes(user.role);

      if (!isAuthor && !isModerator) {
        throw ErrorFactory.authorization('update', 'comment', 'You can only edit your own comments');
      }

      // Get current edit count for version number
      const currentEditCount = existingComment.editCount || 0;
      const newVersionNumber = currentEditCount + 1;

      // Use transaction for atomic operation
      const transaction = this.db.transaction(() => {
        // Store edit history before updating
        const historyStmt = this.db.prepare(`
          INSERT INTO comment_edit_history (comment_id, version_number, previous_content, edited_by)
          VALUES (?, ?, ?, ?)
        `);

        historyStmt.run(commentId, newVersionNumber, existingComment.content, userId);

        // Update comment with new content and edit tracking
        const updateStmt = this.db.prepare(`
          UPDATE project_comments
          SET content = ?,
              is_edited = TRUE,
              edit_count = ?,
              last_edited_at = CURRENT_TIMESTAMP,
              last_edited_by = ?,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `);

        const result = updateStmt.run(sanitizedContent, newVersionNumber, userId, commentId);

        if (result.changes === 0) {
          throw ErrorFactory.databaseOperation('update', 'Failed to update comment');
        }

        return result;
      });

      transaction();

      // Log activity with detailed metadata
      await this.logActivity(
        userId,
        'comment_edit',
        `Edited comment (version ${newVersionNumber})`,
        existingComment.projectId,
        undefined,
        {
          commentId,
          previousContent: existingComment.content,
          newContent: sanitizedContent,
          versionNumber: newVersionNumber,
          editCount: newVersionNumber
        }
      );

      // Get the updated comment with user info including edit details
      const updatedCommentStmt = this.db.prepare(`
        SELECT pc.*,
               u1.username as author_username,
               u1.email as author_email,
               u2.username as last_edited_by_username
        FROM project_comments pc
        JOIN users u1 ON pc.author_id = u1.id
        LEFT JOIN users u2 ON pc.last_edited_by = u2.id
        WHERE pc.id = ?
      `);

      const updatedCommentRow = updatedCommentStmt.get(commentId) as ProjectCommentDbRow;
      if (!updatedCommentRow) {
        throw ErrorFactory.databaseOperation('retrieve', 'Failed to retrieve updated comment');
      }

      return DataMapper.commentFromDb(updatedCommentRow);
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error updating comment:', error);
      throw ErrorFactory.databaseOperation('update', `Failed to update comment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete comment
   */
  async deleteComment(commentId: number, user: { id: string; role: string }): Promise<boolean> {
    // Validate input
    if (!commentId || commentId <= 0) {
      throw ErrorFactory.invalidInput('commentId', commentId, 'Comment ID must be a positive number');
    }

    if (!user?.id) {
      throw ErrorFactory.invalidInput('user', user, 'Valid user is required');
    }

    try {
      // Get existing comment for authorization
      const existingCommentStmt = this.db.prepare(`
        SELECT pc.*, u.username as author_username, u.email as author_email
        FROM project_comments pc
        JOIN users u ON pc.author_id = u.id
        WHERE pc.id = ?
      `);

      const existingCommentRow = existingCommentStmt.get(commentId) as ProjectCommentDbRow;
      if (!existingCommentRow) {
        throw ErrorFactory.notFound('comment', commentId, 'Comment not found');
      }

      const existingComment = DataMapper.commentFromDb(existingCommentRow);

      // Get project for authorization
      const project = this.getProjectById(existingComment.projectId);

      // Check if user can delete this comment (author or moderator+)
      const userId = parseInt(user.id);
      const isAuthor = existingComment.authorId === userId;
      const isModerator = ['moderator', 'admin'].includes(user.role);

      if (!isAuthor && !isModerator) {
        throw ErrorFactory.authorization('delete', 'comment', 'You can only delete your own comments');
      }

      // Delete comment (CASCADE will handle replies)
      const deleteStmt = this.db.prepare(`
        DELETE FROM project_comments
        WHERE id = ?
      `);

      const result = deleteStmt.run(commentId);

      if (result.changes === 0) {
        throw ErrorFactory.databaseOperation('delete', 'Failed to delete comment');
      }

      // Log activity with detailed metadata
      await this.logActivity(
        userId,
        'comment_delete',
        `Deleted comment by ${existingComment.authorUsername || 'Unknown User'}`,
        existingComment.projectId,
        undefined,
        {
          commentId,
          deletedContent: existingComment.content,
          originalAuthorId: existingComment.authorId,
          originalAuthorUsername: existingComment.authorUsername,
          wasEdited: existingComment.isEdited || false,
          editCount: existingComment.editCount || 0
        }
      );

      return true;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error deleting comment:', error);
      throw ErrorFactory.databaseOperation('delete', `Failed to delete comment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get comment edit history
   */
  async getCommentEditHistory(commentId: number, user: { id: string; role: string }): Promise<CommentEditHistory[]> {
    // Validate input
    if (!commentId || commentId <= 0) {
      throw ErrorFactory.invalidInput('commentId', commentId, 'Comment ID must be a positive number');
    }

    if (!user?.id) {
      throw ErrorFactory.invalidInput('user', user, 'Valid user is required');
    }

    try {
      // Get comment to check authorization
      const commentStmt = this.db.prepare(`
        SELECT pc.*, u.username as author_username
        FROM project_comments pc
        JOIN users u ON pc.author_id = u.id
        WHERE pc.id = ?
      `);

      const commentRow = commentStmt.get(commentId) as ProjectCommentDbRow;
      if (!commentRow) {
        throw ErrorFactory.notFound('comment', commentId, 'Comment not found');
      }

      const comment = DataMapper.commentFromDb(commentRow);

      // Universal comment access: Verify project exists but don't restrict to team members
      const project = this.getProjectById(comment.projectId);

      if (!project) {
        throw ErrorFactory.notFound('project', comment.projectId, 'Project not found');
      }

      // Get edit history with user info
      const historyStmt = this.db.prepare(`
        SELECT ceh.*, u.username as edited_by_username
        FROM comment_edit_history ceh
        JOIN users u ON ceh.edited_by = u.id
        WHERE ceh.comment_id = ?
        ORDER BY ceh.version_number DESC
        LIMIT 10
      `);

      const historyRows = historyStmt.all(commentId) as CommentEditHistoryDbRow[];
      return historyRows.map(DataMapper.commentEditHistoryFromDb);
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error getting comment edit history:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve comment edit history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a new task
   */
  async createTask(projectId: number, user: { id: string; role: string }, taskData: CreateTaskInput): Promise<Task> {
    const {
      title,
      description,
      status = 'todo',
      priority = 'medium',
      dueDate,
      assignedTo,
      parentTaskId
    } = taskData;

    // Sanitize and validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    if (!user?.id) {
      throw ErrorFactory.invalidInput('user', user, 'Valid user is required');
    }

    const sanitizedTitle = sanitize.taskTitle(title);
    if (!sanitizedTitle) {
      throw ErrorFactory.invalidInput('title', title, 'Task title is required and cannot be empty');
    }

    const sanitizedDescription = description ? sanitize.taskDescription(description) : null;

    if (status && !['todo', 'inProgress', 'completed'].includes(status)) {
      throw ErrorFactory.invalidInput('status', status, 'Status must be todo, inProgress, or completed');
    }

    if (priority && !['low', 'medium', 'high'].includes(priority)) {
      throw ErrorFactory.invalidInput('priority', priority, 'Priority must be low, medium, or high');
    }

    try {
      // Get full project details for authorization
      const project = this.getProjectById(projectId);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(user as any, { project });

      // Check if user can create tasks in this project
      AuthorizationService.assertCan(authContext, 'create', 'task', 'team');

      // If parentTaskId is provided, validate it exists and belongs to the same project
      if (parentTaskId) {
        const parentTaskStmt = this.db.prepare(`
          SELECT project_id FROM tasks WHERE id = ?
        `);
        const parentTask = parentTaskStmt.get(parentTaskId) as { project_id: number } | undefined;

        if (!parentTask) {
          throw ErrorFactory.notFound('Parent task', parentTaskId);
        }

        if (parentTask.project_id !== projectId) {
          throw ErrorFactory.constraintViolation('parent_task_project_mismatch', 'Parent task must belong to the same project');
        }
      }

      const userId = parseInt(user.id);

      // Use transaction for atomic operation
      const transaction = this.db.transaction(() => {
        // Insert task
        const stmt = this.db.prepare(`
          INSERT INTO tasks (project_id, parent_task_id, title, description, status, priority, due_date, assigned_to, created_by)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
          projectId,
          parentTaskId || null,
          sanitizedTitle,
          sanitizedDescription,
          status,
          priority,
          dueDate || null,
          assignedTo || null,
          userId
        );

        const taskId = result.lastInsertRowid as number;

        // Log activity within transaction
        const taskType = parentTaskId ? 'subtask' : 'task';
        let activityDescription = `Created ${taskType} "${sanitizedTitle}"`;

        // For subtasks, include parent task context
        if (parentTaskId) {
          const parentTaskStmt = this.db.prepare(`
            SELECT title FROM tasks WHERE id = ?
          `);
          const parentTask = parentTaskStmt.get(parentTaskId) as { title: string } | undefined;
          if (parentTask) {
            activityDescription = `Created subtask "${sanitizedTitle}" in task "${parentTask.title}"`;
          }
        }

        const activityStmt = this.db.prepare(`
          INSERT INTO activity_log (project_id, task_id, user_id, activity_type, description, created_at)
          VALUES (?, ?, ?, ?, ?, datetime('now'))
        `);

        activityStmt.run(projectId, taskId, userId, 'task_creation', activityDescription);

        // Note: Progress update moved outside transaction to prevent locks

        return taskId;
      });

      const taskId = transaction();

      // Update project progress outside transaction to prevent locks
      try {
        this.updateProjectProgressSync(projectId);
      } catch (progressError) {
        console.warn('Failed to update project progress:', progressError);
      }

      // Return the created task
      return this.getTaskById(taskId);
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error creating task:', error);
      throw ErrorFactory.databaseOperation('create', `Failed to create task: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get task by ID
   */
  getTaskById(taskId: number): Task {
    // Validate input
    if (!taskId || taskId <= 0) {
      throw ErrorFactory.invalidInput('taskId', taskId, 'Task ID must be a positive number');
    }

    try {
      const stmt = this.db.prepare(`
        SELECT t.*, u1.username as created_by_username, u2.username as assigned_to_username
        FROM tasks t
        LEFT JOIN users u1 ON t.created_by = u1.id
        LEFT JOIN users u2 ON t.assigned_to = u2.id
        WHERE t.id = ?
      `);

      const taskRow = stmt.get(taskId) as TaskDbRow;
      if (!taskRow) {
        throw ErrorFactory.notFound('Task', taskId);
      }

      // Convert database row to API format
      const task = DataMapper.taskFromDb(taskRow);

      // Get subtasks if this is a main task
      if (!task.parentTaskId) {
        const subtasksStmt = this.db.prepare(`
          SELECT t.*, u1.username as created_by_username, u2.username as assigned_to_username
          FROM tasks t
          LEFT JOIN users u1 ON t.created_by = u1.id
          LEFT JOIN users u2 ON t.assigned_to = u2.id
          WHERE t.parent_task_id = ?
          ORDER BY t.created_at ASC
        `);
        const subtaskRows = subtasksStmt.all(taskId) as TaskDbRow[];
        task.subtasks = subtaskRows.map(DataMapper.taskFromDb);
      }

      return task;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error getting task by ID:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve task ${taskId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get all tasks for a project
   */
  getProjectTasks(projectId: number): Task[] {
    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    try {
      // Get all tasks for the project in one query to avoid N+1 problem
      const allTasksStmt = this.db.prepare(`
        SELECT t.*, u1.username as created_by_username, u2.username as assigned_to_username
        FROM tasks t
        LEFT JOIN users u1 ON t.created_by = u1.id
        LEFT JOIN users u2 ON t.assigned_to = u2.id
        WHERE t.project_id = ?
        ORDER BY t.parent_task_id IS NULL DESC, t.created_at ASC
      `);

      const allTaskRows = allTasksStmt.all(projectId) as TaskDbRow[];

      // Separate main tasks and subtasks
      const mainTaskRows = allTaskRows.filter(row => row.parent_task_id === null);
      const subtaskRows = allTaskRows.filter(row => row.parent_task_id !== null);

      // Group subtasks by parent task ID
      const subtasksByParent = new Map<number, TaskDbRow[]>();
      subtaskRows.forEach(subtask => {
        const parentId = subtask.parent_task_id!;
        if (!subtasksByParent.has(parentId)) {
          subtasksByParent.set(parentId, []);
        }
        subtasksByParent.get(parentId)!.push(subtask);
      });

      return mainTaskRows.map(taskRow => {
        try {
          // Convert database row to API format
          const task = DataMapper.taskFromDb(taskRow);

          // Get subtasks for this main task
          const taskSubtasks = subtasksByParent.get(task.id) || [];
          task.subtasks = taskSubtasks.map(DataMapper.taskFromDb);

          return task;
        } catch (subtaskError) {
          console.warn(`Error loading subtasks for task ${taskRow.id}:`, subtaskError);
          // Convert main task even if subtasks fail
          const task = DataMapper.taskFromDb(taskRow);
          task.subtasks = [];
          return task;
        }
      });
    } catch (error) {
      console.error('Error getting project tasks:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve tasks for project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update task
   */
  async updateTask(taskId: number, user: { id: string; role: string }, updateData: UpdateTaskData): Promise<boolean> {
    try {
      const fields: string[] = [];
      const values: (string | number | boolean | null)[] = [];

      // Build dynamic update query
      Object.entries(updateData).forEach(([key, value]) => {
        if (value !== undefined) {
          fields.push(`${key} = ?`);
          values.push(value);
        }
      });

      if (fields.length === 0) {
        return true; // No updates needed
      }

      fields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(taskId);

      // Get task info before update for authorization and logging
      const task = this.getTaskById(taskId);
      if (!task) {
        return false;
      }

      // Get project for authorization
      const project = this.getProjectById(task.projectId);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(user as any, {
        project,
        resource: task
      });

      // Check if user can update this task
      AuthorizationService.assertCan(authContext, 'update', 'task', 'team');

      // Use transaction for atomic operation
      const transaction = this.db.transaction(() => {
        // Update task
        const stmt = this.db.prepare(`
          UPDATE tasks
          SET ${fields.join(', ')}
          WHERE id = ?
        `);

        const result = stmt.run(...values);

        if (result.changes > 0) {
          // Log activity for significant changes within transaction
          if (updateData.status) {
            const activityStmt = this.db.prepare(`
              INSERT INTO activity_log (project_id, task_id, user_id, activity_type, description, created_at)
              VALUES (?, ?, ?, ?, ?, datetime('now'))
            `);

            // Use column_move for status changes to better track Kanban movements
            const activityType = 'column_move';
            const statusDisplayMap = {
              'todo': 'To Do',
              'inProgress': 'In Progress',
              'completed': 'Completed'
            };

            const statusDisplay = statusDisplayMap[updateData.status as keyof typeof statusDisplayMap] || updateData.status;
            const description = `Moved "${task.title}" to ${statusDisplay}`;

            activityStmt.run(
              task.projectId,
              taskId,
              parseInt(user.id),
              activityType,
              description
            );

            // If task completed, check if parent task should be completed
            if (updateData.status === 'completed') {
              // Note: checkParentTaskCompletion is currently disabled
              // but we keep the call for future implementation
            }
          }

          // Note: Progress update moved outside transaction to prevent locks
        }

        return result.changes > 0;
      });

      const result = transaction();

      // Update project progress outside transaction to prevent locks
      if (result) {
        try {
          this.updateProjectProgressSync(task.projectId);
        } catch (progressError) {
          console.warn('Failed to update project progress:', progressError);
        }
      }

      return result;
    } catch (error) {
      console.error('Error updating task:', error);
      return false;
    }
  }

  /**
   * Delete task
   */
  async deleteTask(taskId: number, user: { id: string; role: string }): Promise<boolean> {
    try {
      // Get task info before deletion
      const task = this.getTaskById(taskId);
      if (!task) {
        return false;
      }

      // Get project for authorization
      const project = this.getProjectById(task.projectId);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(user as any, {
        project,
        resource: task
      });

      // Check if user can delete this task (must be owner or have team permissions)
      AuthorizationService.assertCan(authContext, 'delete', 'task', 'team');

      const userId = parseInt(user.id);

      // Use transaction for atomic operation
      const transaction = this.db.transaction(() => {
        // Log activity BEFORE deleting the task (to avoid foreign key constraint issues)
        const activityStmt = this.db.prepare(`
          INSERT INTO activity_log (project_id, user_id, activity_type, description, created_at)
          VALUES (?, ?, ?, ?, datetime('now'))
        `);

        activityStmt.run(
          task.projectId,
          userId,
          'update',
          `Deleted task "${task.title}"`
        );

        // Delete task
        const stmt = this.db.prepare(`
          DELETE FROM tasks WHERE id = ? AND created_by = ?
        `);

        const result = stmt.run(taskId, userId);

        if (result.changes === 0) {
          // Check if task exists but user is not the creator
          const taskExistsStmt = this.db.prepare(`SELECT created_by FROM tasks WHERE id = ?`);
          const existingTask = taskExistsStmt.get(taskId) as { created_by: number } | undefined;

          if (existingTask) {
            throw ErrorFactory.authorization('delete', 'task', 'You can only delete tasks you created');
          } else {
            throw ErrorFactory.notFound('Task', taskId);
          }
        }

        // Note: Progress update moved outside transaction to prevent locks
        return true;
      });

      const result = transaction();

      // Update project progress outside transaction to prevent locks
      if (result) {
        try {
          this.updateProjectProgressSync(task.projectId);
        } catch (progressError) {
          console.warn('Failed to update project progress:', progressError);
        }
      }

      return result;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error deleting task:', error);
      throw ErrorFactory.databaseOperation('delete', `Failed to delete task ${taskId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle project field edit with version control (with current content provided)
   */
  async handleProjectFieldEditWithCurrentData(
    projectId: number,
    userId: number,
    fieldName: 'full_description' | 'event_log',
    newContent: string,
    currentContent?: string
  ): Promise<void> {
    try {
      // Skip if content hasn't changed
      if (currentContent === newContent) {
        return;
      }

      // Get current edit count for this field
      const editCountField = `${fieldName}_edit_count`;
      const editCountStmt = this.db.prepare(`
        SELECT ${editCountField} as edit_count FROM projects WHERE id = ?
      `);
      const editCountRow = editCountStmt.get(projectId) as { edit_count: number | null };
      const currentEditCount = editCountRow?.edit_count || 0;
      const newVersionNumber = currentEditCount + 1;

      // ALWAYS store edit history, regardless of whether previous content was empty or not
      // This ensures we track the complete edit history including initial states
      const historyStmt = this.db.prepare(`
        INSERT INTO project_edit_history (project_id, field_name, version_number, previous_content, edited_by)
        VALUES (?, ?, ?, ?, ?)
      `);
      historyStmt.run(projectId, fieldName, newVersionNumber, currentContent || '', userId);

      // Update edit tracking fields
      const updateEditTrackingStmt = this.db.prepare(`
        UPDATE projects
        SET ${fieldName}_edit_count = ?,
            ${fieldName}_last_edited_at = CURRENT_TIMESTAMP,
            ${fieldName}_last_edited_by = ?
        WHERE id = ?
      `);
      updateEditTrackingStmt.run(newVersionNumber, userId, projectId);

      // Log activity with metadata
      const activityType = fieldName === 'full_description' ? 'project_details_edit' : 'event_log_edit';
      const fieldDisplayName = fieldName === 'full_description' ? 'Project Details' : 'Event Log';

      await this.logActivity(
        userId,
        activityType,
        `edited ${fieldDisplayName} (V${newVersionNumber})`,
        projectId,
        undefined,
        {
          fieldName,
          previousContent: currentContent || '',
          newContent,
          versionNumber: newVersionNumber,
          editCount: newVersionNumber
        }
      );
    } catch (error) {
      console.error(`Error handling ${fieldName} edit:`, error);
    }
  }

  /**
   * Handle project field edit with version control (legacy method)
   */
  async handleProjectFieldEdit(
    projectId: number,
    userId: number,
    fieldName: 'full_description' | 'event_log',
    newContent: string
  ): Promise<void> {
    try {
      // Get current project data
      const project = this.getProjectById(projectId);
      if (!project) {
        throw ErrorFactory.notFound('project', projectId, 'Project not found');
      }

      const currentContent = fieldName === 'full_description' ? project.fullDescription : project.eventLog;

      // Call the main method with current content
      await this.handleProjectFieldEditWithCurrentData(projectId, userId, fieldName, newContent, currentContent);
    } catch (error) {
      console.error(`Error handling ${fieldName} edit:`, error);
    }
  }

  /**
   * Get project field edit history
   */
  async getProjectFieldEditHistory(
    projectId: number,
    fieldName: 'full_description' | 'event_log',
    user: { id: string; role: string }
  ): Promise<ProjectEditHistory[]> {
    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    if (!user?.id) {
      throw ErrorFactory.invalidInput('user', user, 'Valid user is required');
    }

    try {
      // Get project for authorization
      const project = this.getProjectById(projectId);
      if (!project) {
        throw ErrorFactory.notFound('project', projectId, 'Project not found');
      }

      // Create authorization context
      const authContext = AuthorizationService.createContext(user as any, {
        project
      });

      // Check if user has access to this project (team member)
      AuthorizationService.assertCan(authContext, 'read', 'project', 'team');

      // Get edit history with user info
      const historyStmt = this.db.prepare(`
        SELECT peh.*, u.username as edited_by_username
        FROM project_edit_history peh
        JOIN users u ON peh.edited_by = u.id
        WHERE peh.project_id = ? AND peh.field_name = ?
        ORDER BY peh.version_number DESC
        LIMIT 10
      `);

      const historyRows = historyStmt.all(projectId, fieldName) as ProjectEditHistoryDbRow[];
      return historyRows.map(DataMapper.projectEditHistoryFromDb);
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error getting project field edit history:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve ${fieldName} edit history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Log activity
   */
  async logActivity(
    userId: number,
    activityType: ActivityLogEntry['activity_type'],
    description: string,
    projectId?: number,
    taskId?: number,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    try {
      const stmt = this.db.prepare(`
        INSERT INTO activity_log (project_id, task_id, user_id, activity_type, description, metadata)
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        projectId || null,
        taskId || null,
        userId,
        activityType,
        description,
        metadata ? JSON.stringify(metadata) : null
      );
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  }

  /**
   * Get activity log for a project with pagination
   */
  getProjectActivity(projectId: number, options?: {
    limit?: number;
    offset?: number;
    taskId?: number;
  }): ActivityLogEntry[] {
    const { limit = 50, offset = 0, taskId } = options || {};

    try {
      let query = `
        SELECT al.*, u.username
        FROM activity_log al
        JOIN users u ON al.user_id = u.id
        WHERE al.project_id = ?
      `;

      const params: (string | number)[] = [projectId];

      if (taskId) {
        query += ` AND al.task_id = ?`;
        params.push(taskId);
      }

      query += ` ORDER BY al.created_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const stmt = this.db.prepare(query);
      const rows = stmt.all(...params) as ActivityLogEntryDbRow[];
      return rows.map(DataMapper.activityFromDb);
    } catch (error) {
      console.error('Error getting project activity:', error);
      return [];
    }
  }

  /**
   * Get activity count for a project (for pagination)
   */
  getProjectActivityCount(projectId: number, taskId?: number): number {
    try {
      let query = `
        SELECT COUNT(*) as count
        FROM activity_log
        WHERE project_id = ?
      `;

      const params: (string | number)[] = [projectId];

      if (taskId) {
        query += ` AND task_id = ?`;
        params.push(taskId);
      }

      const stmt = this.db.prepare(query);
      const result = stmt.get(...params) as { count: number };
      return result.count;
    } catch (error) {
      console.error('Error getting project activity count:', error);
      return 0;
    }
  }

  /**
   * Get recent activity across all projects for a user
   */
  getUserActivity(userId: number): ActivityLogEntry[] {
    try {
      const stmt = this.db.prepare(`
        SELECT al.*, u.username
        FROM activity_log al
        JOIN users u ON al.user_id = u.id
        WHERE al.project_id IN (
          SELECT id FROM projects
          WHERE created_by = ? OR assigned_to = ? OR id IN (
            SELECT project_id FROM project_team_members WHERE user_id = ?
          )
        )
        ORDER BY al.created_at DESC
        LIMIT 100
      `);

      const rows = stmt.all(userId, userId, userId) as ActivityLogEntryDbRow[];
      return rows.map(DataMapper.activityFromDb);
    } catch (error) {
      console.error('Error getting user activity:', error);
      return [];
    }
  }

  /**
   * Get enhanced activity data for dashboard with current titles and deduplication
   */
  getDashboardActivity(userId: number): ActivityLogEntry[] {
    try {
      const stmt = this.db.prepare(`
        SELECT DISTINCT
          al.*,
          u.username,
          p.title as project_title,
          t.title as task_title,
          p.status as project_status,
          t.status as task_status
        FROM activity_log al
        JOIN users u ON al.user_id = u.id
        LEFT JOIN projects p ON al.project_id = p.id
        LEFT JOIN tasks t ON al.task_id = t.id
        WHERE al.project_id IN (
          SELECT id FROM projects
          WHERE created_by = ? OR assigned_to = ? OR id IN (
            SELECT project_id FROM project_team_members WHERE user_id = ?
          )
        )
        AND al.activity_type IN ('task_creation', 'creation', 'column_move')
        ORDER BY al.created_at DESC
        LIMIT 50
      `);

      const rows = stmt.all(userId, userId, userId) as (ActivityLogEntryDbRow & {
        project_title?: string;
        task_title?: string;
        project_status?: string;
        task_status?: string;
      })[];

      // Filter and deduplicate activities
      const filteredRows = this.filterAndDeduplicateActivities(rows);

      return filteredRows.map(row => ({
        ...DataMapper.activityFromDb(row),
        // Add enhanced data for dashboard formatting
        projectTitle: row.project_title,
        taskTitle: row.task_title,
        projectStatus: row.project_status,
        taskStatus: row.task_status,
      })) as ActivityLogEntry[];
    } catch (error) {
      console.error('Error getting dashboard activity:', error);
      return [];
    }
  }

  /**
   * Filter and deduplicate activities for dashboard display
   */
  private filterAndDeduplicateActivities(
    rows: (ActivityLogEntryDbRow & {
      project_title?: string;
      task_title?: string;
      project_status?: string;
      task_status?: string;
    })[]
  ) {
    const filtered: typeof rows = [];
    const seen = new Set<string>();

    for (const row of rows) {
      // Create unique key for deduplication
      let key = '';

      if (row.activity_type === 'creation') {
        key = `creation-${row.project_id}`;
      } else if (row.activity_type === 'task_creation') {
        key = `task_creation-${row.task_id}`;
      } else if (row.activity_type === 'column_move') {
        // Track column movements with current status
        const status = row.description.includes('To Do') ? 'todo' :
                      row.description.includes('In Progress') ? 'inProgress' :
                      row.description.includes('Completed') ? 'completed' : 'unknown';

        if (row.task_id) {
          key = `column_move-task-${row.task_id}-${status}`;
        } else {
          key = `column_move-project-${row.project_id}-${status}`;
        }
      } else {
        continue; // Skip other activity types
      }

      // Skip if we've already seen this activity
      if (seen.has(key)) {
        continue;
      }

      seen.add(key);
      filtered.push(row);

      // Limit to 10 items for dashboard
      if (filtered.length >= 10) {
        break;
      }
    }

    return filtered;
  }

  /**
   * Update project progress based on task completion (optimized version)
   */
  private updateProjectProgressSync(projectId: number): void {
    try {
      // Use a single optimized query to calculate progress
      const stmt = this.db.prepare(`
        SELECT
          COUNT(*) as total_tasks,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks
        FROM tasks
        WHERE project_id = ?
      `);

      const result = stmt.get(projectId) as { total_tasks: number; completed_tasks: number };

      let progress = 0;
      if (result.total_tasks > 0) {
        progress = Math.round((result.completed_tasks / result.total_tasks) * 100);
      }

      // Update project progress
      const updateStmt = this.db.prepare(`
        UPDATE projects SET progress = ? WHERE id = ?
      `);
      updateStmt.run(progress, projectId);

      // Note: Removed automatic project completion when progress reaches 100%
      // Projects should only move to 'completed' status when manually dragged by user
    } catch (error) {
      console.error('Error updating project progress:', error);
      // Don't throw error to prevent cascading failures
    }
  }

  /**
   * Update project progress based on task completion (async version for backward compatibility)
   */
  private async updateProjectProgress(projectId: number): Promise<void> {
    try {
      this.updateProjectProgressSync(projectId);
    } catch (error) {
      console.error('Error updating project progress:', error);
    }
  }

  /**
   * Check if parent task should be completed when subtask is completed
   * Note: Removed automatic parent task completion - tasks should only be completed manually
   */
  private async checkParentTaskCompletion(task: Task): Promise<void> {
    // Disabled automatic parent task completion
    // Parent tasks should only be marked as completed when manually done by user
    // This prevents automatic movement between columns when all subtasks are done
    return;
  }

  /**
   * Add file to project
   */
  async addProjectFile(
    projectId: number,
    user: { id: string; role: string },
    fileData: {
      file_name: string;
      file_type?: string;
      file_size?: number;
      file_path: string;
      thumbnail_path?: string;
    }
  ): Promise<ProjectFile | null> {
    try {
      // Get project for authorization
      const project = this.getProjectById(projectId);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(user as any, {
        project
      });

      // Check if user can upload files to this project
      AuthorizationService.assertCan(authContext, 'create', 'file', 'team');

      const userId = parseInt(user.id);

      // Sanitize file name
      const sanitizedFileName = sanitize.fileName(fileData.file_name);
      if (!sanitizedFileName) {
        throw ErrorFactory.invalidInput('file_name', fileData.file_name, 'Valid file name is required');
      }

      const stmt = this.db.prepare(`
        INSERT INTO project_files (project_id, file_name, file_type, file_size, file_path, thumbnail_path, uploaded_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      const result = stmt.run(
        projectId,
        sanitizedFileName,
        fileData.file_type || null,
        fileData.file_size || null,
        fileData.file_path,
        fileData.thumbnail_path || null,
        userId
      );

      const fileId = result.lastInsertRowid as number;

      // Log activity
      await this.logActivity(userId, 'upload', `Uploaded file "${sanitizedFileName}"`, projectId);

      // Get the created file with user info
      const fileStmt = this.db.prepare(`
        SELECT pf.*, u.username as uploaded_by_username
        FROM project_files pf
        JOIN users u ON pf.uploaded_by = u.id
        WHERE pf.id = ?
      `);

      const file = fileStmt.get(fileId) as ProjectFile;
      if (!file) {
        throw ErrorFactory.databaseOperation('retrieve', 'Failed to retrieve created file');
      }

      return file;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error adding project file:', error);
      throw ErrorFactory.fileOperation('upload', `Failed to add file: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        fileName: fileData.file_name,
        operation: 'upload'
      });
    }
  }

  /**
   * Get files for a project with pagination
   */
  getProjectFiles(projectId: number, options?: {
    limit?: number;
    offset?: number;
  }): ProjectFile[] {
    const { limit = 20, offset = 0 } = options || {};

    try {
      const stmt = this.db.prepare(`
        SELECT pf.*, u.username as uploaded_by_username
        FROM project_files pf
        JOIN users u ON pf.uploaded_by = u.id
        WHERE pf.project_id = ?
        ORDER BY pf.uploaded_at DESC
        LIMIT ? OFFSET ?
      `);

      return stmt.all(projectId, limit, offset) as ProjectFile[];
    } catch (error) {
      console.error('Error getting project files:', error);
      return [];
    }
  }

  /**
   * Get file count for a project (for pagination)
   */
  getProjectFileCount(projectId: number): number {
    try {
      const stmt = this.db.prepare(`
        SELECT COUNT(*) as count
        FROM project_files
        WHERE project_id = ?
      `);

      const result = stmt.get(projectId) as { count: number };
      return result.count;
    } catch (error) {
      console.error('Error getting project file count:', error);
      return 0;
    }
  }

  /**
   * Get file by ID
   */
  getFileById(fileId: number): ProjectFile {
    // Validate input
    if (!fileId || fileId <= 0) {
      throw ErrorFactory.invalidInput('fileId', fileId, 'File ID must be a positive number');
    }

    try {
      const stmt = this.db.prepare(`
        SELECT pf.*, u.username as uploaded_by_username
        FROM project_files pf
        JOIN users u ON pf.uploaded_by = u.id
        WHERE pf.id = ?
      `);

      const file = stmt.get(fileId) as ProjectFile;
      if (!file) {
        throw ErrorFactory.notFound('File', fileId);
      }

      return file;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error getting file by ID:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve file ${fileId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete project file
   */
  async deleteProjectFile(fileId: number, user: { id: string; role: string }): Promise<boolean> {
    try {
      // Get file info before deletion
      const fileStmt = this.db.prepare(`
        SELECT * FROM project_files WHERE id = ?
      `);
      const file = fileStmt.get(fileId) as ProjectFile;

      if (!file) {
        return false;
      }

      // Get project for authorization
      const project = this.getProjectById(file.project_id);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(user as any, {
        project,
        resource: file
      });

      // Check if user can delete this file (must be uploader or have team permissions)
      AuthorizationService.assertCan(authContext, 'delete', 'file', 'team');

      const userId = parseInt(user.id);

      const deleteStmt = this.db.prepare(`
        DELETE FROM project_files WHERE id = ?
      `);

      const result = deleteStmt.run(fileId);

      if (result.changes > 0) {
        // Log activity
        await this.logActivity(userId, 'upload', `Deleted file "${file.file_name}"`, file.project_id);
      }

      if (result.changes === 0) {
        throw ErrorFactory.databaseOperation('delete', 'No rows were deleted');
      }

      return true;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error deleting project file:', error);
      throw ErrorFactory.fileOperation('delete', `Failed to delete file ${fileId}: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        operation: 'delete'
      });
    }
  }

  /**
   * Get project comments with pagination and threading support
   */
  getProjectComments(projectId: number, options?: {
    limit?: number;
    offset?: number;
    includeReplies?: boolean;
    sortOrder?: 'asc' | 'desc';
  }): ProjectComment[] {
    const {
      limit = 20,
      offset = 0,
      includeReplies = true,
      sortOrder = 'asc'
    } = options || {};

    try {
      // First get top-level comments
      const topLevelStmt = this.db.prepare(`
        SELECT pc.*,
               u1.username as author_username,
               u1.email as author_email,
               u2.username as last_edited_by_username
        FROM project_comments pc
        JOIN users u1 ON pc.author_id = u1.id
        LEFT JOIN users u2 ON pc.last_edited_by = u2.id
        WHERE pc.project_id = ? AND pc.parent_comment_id IS NULL
        ORDER BY pc.created_at ${sortOrder.toUpperCase()}
        LIMIT ? OFFSET ?
      `);

      const topLevelRows = topLevelStmt.all(projectId, limit, offset) as ProjectCommentDbRow[];
      const topLevelComments = topLevelRows.map(DataMapper.commentFromDb);

      // If replies are requested, get them all at once to avoid N+1 queries
      if (includeReplies && topLevelComments.length > 0) {
        const topLevelIds = topLevelComments.map(c => c.id);
        const placeholders = topLevelIds.map(() => '?').join(',');

        const repliesStmt = this.db.prepare(`
          SELECT pc.*,
                 u1.username as author_username,
                 u1.email as author_email,
                 u2.username as last_edited_by_username
          FROM project_comments pc
          JOIN users u1 ON pc.author_id = u1.id
          LEFT JOIN users u2 ON pc.last_edited_by = u2.id
          WHERE pc.parent_comment_id IN (${placeholders})
          ORDER BY pc.parent_comment_id, pc.created_at ASC
        `);

        try {
          const allReplyRows = repliesStmt.all(...topLevelIds) as ProjectCommentDbRow[];
          const repliesByParent = new Map<string, ProjectCommentDbRow[]>();

          // Group replies by parent comment ID
          allReplyRows.forEach(reply => {
            const parentId = reply.parent_comment_id!.toString();
            if (!repliesByParent.has(parentId)) {
              repliesByParent.set(parentId, []);
            }
            repliesByParent.get(parentId)!.push(reply);
          });

          // Attach replies to their parent comments
          return topLevelComments.map(comment => ({
            ...comment,
            replies: (repliesByParent.get(comment.id.toString()) || []).map(DataMapper.commentFromDb)
          }));
        } catch (replyError) {
          console.warn('Error loading replies for comments:', replyError);
          return topLevelComments.map(comment => ({ ...comment, replies: [] }));
        }
      }

      return topLevelComments;
    } catch (error) {
      console.error('Error getting project comments:', error);
      return [];
    }
  }

  /**
   * Get comment count for a project (for pagination)
   */
  getProjectCommentCount(projectId: number, includeReplies: boolean = true): number {
    try {
      let query = `
        SELECT COUNT(*) as count
        FROM project_comments
        WHERE project_id = ?
      `;

      if (!includeReplies) {
        query += ` AND parent_comment_id IS NULL`;
      }

      const stmt = this.db.prepare(query);
      const result = stmt.get(projectId) as { count: number };
      return result.count;
    } catch (error) {
      console.error('Error getting project comment count:', error);
      return 0;
    }
  }

  /**
   * Get team members for a project
   * ENHANCED: Now uses proper field mapping to convert snake_case DB to camelCase API
   */
  getProjectTeamMembers(projectId: number): ProjectTeamMember[] {
    try {
      const stmt = this.db.prepare(`
        SELECT ptm.*, u.username, u.email
        FROM project_team_members ptm
        JOIN users u ON ptm.user_id = u.id
        WHERE ptm.project_id = ?
        ORDER BY ptm.added_at ASC
      `);

      const rows = stmt.all(projectId) as ProjectTeamMemberDbRow[];

      // CRITICAL FIX: Use enhanced DataMapper to properly convert fields and set name
      return DataMapper.teamMembersFromDb(rows);
    } catch (error) {
      console.error('Error getting project team members:', error);
      return [];
    }
  }

  /**
   * Add team member to project
   */
  async addTeamMember(
    projectId: number,
    addedByUser: { id: string; role: string },
    userEmail: string,
    role: string = 'member'
  ): Promise<ProjectTeamMember | null> {
    try {
      // Get project for authorization
      const project = this.getProjectById(projectId);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(addedByUser as any, {
        project
      });

      // Check if user can invite team members to this project
      AuthorizationService.assertCan(authContext, 'invite', 'team_member', 'team');

      // Sanitize and validate email
      const sanitizedEmail = sanitize.userEmail(userEmail);
      if (!sanitizedEmail) {
        throw ErrorFactory.invalidInput('userEmail', userEmail, 'Valid email address is required');
      }

      // Find user by email
      const userStmt = this.db.prepare(`
        SELECT id FROM users WHERE email = ?
      `);
      const user = userStmt.get(sanitizedEmail) as { id: number } | undefined;

      if (!user) {
        throw ErrorFactory.notFound('User', userEmail);
      }

      // Check if user is already a team member
      const existingStmt = this.db.prepare(`
        SELECT id FROM project_team_members WHERE project_id = ? AND user_id = ?
      `);
      const existing = existingStmt.get(projectId, user.id);

      if (existing) {
        throw ErrorFactory.businessLogic('User is already a team member', {
          projectId,
          userEmail,
          userId: user.id
        });
      }

      const addedBy = parseInt(addedByUser.id);

      // Use transaction for atomic operation
      const transaction = this.db.transaction(() => {
        // Add team member
        const stmt = this.db.prepare(`
          INSERT INTO project_team_members (project_id, user_id, role, added_by)
          VALUES (?, ?, ?, ?)
        `);

        const result = stmt.run(projectId, user.id, role, addedBy);
        const memberId = result.lastInsertRowid as number;

        // Log activity within transaction
        const activityStmt = this.db.prepare(`
          INSERT INTO activity_log (project_id, user_id, activity_type, description, created_at)
          VALUES (?, ?, ?, ?, datetime('now'))
        `);

        activityStmt.run(projectId, addedBy, 'assignment', `Added ${sanitizedEmail} to project team`);

        return memberId;
      });

      const memberId = transaction();

      // Get the created team member with user info
      const memberStmt = this.db.prepare(`
        SELECT ptm.*, u.username, u.email
        FROM project_team_members ptm
        JOIN users u ON ptm.user_id = u.id
        WHERE ptm.id = ?
      `);

      const memberRow = memberStmt.get(memberId) as ProjectTeamMemberDbRow;
      if (!memberRow) {
        throw ErrorFactory.databaseOperation('retrieve', 'Failed to retrieve created team member');
      }

      // CRITICAL FIX: Use enhanced DataMapper to properly convert fields and set name
      const member = DataMapper.safeTeamMemberFromDb(memberRow);
      if (!member) {
        throw ErrorFactory.databaseOperation('retrieve', 'Failed to map created team member');
      }

      return member;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error adding team member:', error);
      throw ErrorFactory.databaseOperation('create', `Failed to add team member: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Remove team member from project
   */
  async removeTeamMember(projectId: number, memberId: number, removedByUser: { id: string; role: string }): Promise<boolean> {
    try {
      // Get project for authorization
      const project = this.getProjectById(projectId);

      // Create authorization context and check permissions
      const authContext = AuthorizationService.createContext(removedByUser as any, {
        project
      });

      // Check if user can remove team members from this project
      AuthorizationService.assertCan(authContext, 'delete', 'team_member', 'team');

      // Get member info before deletion
      const memberStmt = this.db.prepare(`
        SELECT ptm.*, u.email
        FROM project_team_members ptm
        JOIN users u ON ptm.user_id = u.id
        WHERE ptm.id = ? AND ptm.project_id = ?
      `);
      const member = memberStmt.get(memberId, projectId) as (ProjectTeamMember & { email: string }) | undefined;

      if (!member) {
        throw ErrorFactory.notFound('Team member', memberId);
      }

      const removedBy = parseInt(removedByUser.id);

      // Use transaction for atomic operation
      const transaction = this.db.transaction(() => {
        // Delete team member
        const deleteStmt = this.db.prepare(`
          DELETE FROM project_team_members WHERE id = ? AND project_id = ?
        `);

        const result = deleteStmt.run(memberId, projectId);

        if (result.changes > 0) {
          // Log activity within transaction
          const activityStmt = this.db.prepare(`
            INSERT INTO activity_log (project_id, user_id, activity_type, description, created_at)
            VALUES (?, ?, ?, ?, datetime('now'))
          `);

          activityStmt.run(projectId, removedBy, 'assignment', `Removed ${member.email} from project team`);
        }

        if (result.changes === 0) {
          throw ErrorFactory.databaseOperation('delete', 'No rows were deleted');
        }

        return true;
      });

      return transaction();
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error removing team member:', error);
      throw ErrorFactory.databaseOperation('delete', `Failed to remove team member ${memberId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
