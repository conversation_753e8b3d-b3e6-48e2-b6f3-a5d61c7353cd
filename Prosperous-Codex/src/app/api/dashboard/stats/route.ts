import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/task-master-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const taskMasterService = new TaskMasterService();
    
    // Get all user projects
    const allProjects = taskMasterService.getUserProjects(parseInt(user.id));
    
    // Calculate active projects (not completed)
    const activeProjects = allProjects.filter(project => project.status !== 'completed');
    const activeProjectsCount = activeProjects.length;
    
    // Calculate new projects (created within last 24 hours)
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const newProjects = activeProjects.filter(project => {
      const createdAt = new Date(project.createdAt);
      return createdAt >= twentyFourHoursAgo;
    });
    const newProjectsCount = newProjects.length;
    
    // Get recent activity - filter for task creation and active task activities only
    const allActivity = taskMasterService.getUserActivity(parseInt(user.id));
    const recentActivity = allActivity
      .filter(activity =>
        activity.activityType === 'task_creation' ||
        activity.activityType === 'creation' ||
        (activity.activityType === 'status_change' && activity.description.includes('inProgress'))
      )
      .slice(0, 10);
    
    return NextResponse.json({
      activeProjects: {
        count: activeProjectsCount,
        newCount: newProjectsCount
      },
      recentActivity
    });
    
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
