import { ActivityLogEntry } from '@/lib/task-master/types';

/**
 * Dashboard statistics response from API
 */
export interface DashboardStats {
  activeProjects: {
    count: number;
    newCount: number;
  };
  recentActivity: ActivityLogEntry[];
}

/**
 * Formatted activity item for UI display
 */
export interface FormattedActivity {
  id: number;
  icon: React.ReactNode;
  variant: 'success' | 'neutral' | 'warning' | 'error';
  title: string;
  description: string;
  time: string;
}

/**
 * Activity type to icon and variant mapping
 */
export interface ActivityMapping {
  icon: React.ReactNode;
  variant: 'success' | 'neutral' | 'warning' | 'error';
  titlePrefix?: string;
}
